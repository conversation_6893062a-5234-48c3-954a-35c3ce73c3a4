{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake", "cpack": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cpack", "ctest": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/ctest", "root": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-070d060a027fcd574459.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-40a2ad591554d1b03370.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-fc890176fec95f0c20c2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-ec1725459884363f77ed.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-40a2ad591554d1b03370.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-fc890176fec95f0c20c2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-070d060a027fcd574459.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, "toolchains-v1": {"jsonFile": "toolchains-v1-ec1725459884363f77ed.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}