{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.30"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "untitled1", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "untitled1::@6890427a1f51a3e7e1df", "jsonFile": "target-untitled1-Debug-4a0abe84353642b7d200.json", "name": "untitled1", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/CLionProjects/untitled1/cmake-build-debug", "source": "/Users/<USER>/CLionProjects/untitled1"}, "version": {"major": 2, "minor": 7}}