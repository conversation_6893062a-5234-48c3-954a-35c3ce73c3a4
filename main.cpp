#include <iostream>
#include <cmath>

/**
 * 判断目标相对于飞机的方向位置
 * @param aircraft_lon 飞机经度（度）
 * @param aircraft_lat 飞机纬度（度）
 * @param aircraft_heading 飞机航向角（度，0°为正北，顺时针递增）
 * @param target_lon 目标经度（度）
 * @param target_lat 目标纬度（度）
 * @param threshold 判断阈值（度，默认5°）
 * @return int 返回值：-1=左侧，0=正前方/正后方，+1=右侧
 * <AUTHOR>
 */
int getTargetDirection(double aircraft_lon, double aircraft_lat, double aircraft_heading,
                      double target_lon, double target_lat, double threshold = 5.0) {

    // 将角度转换为弧度
    const double PI = 3.14159265359;
    double lat1 = aircraft_lat * PI / 180.0;
    double lon1 = aircraft_lon * PI / 180.0;
    double lat2 = target_lat * PI / 180.0;
    double lon2 = target_lon * PI / 180.0;

    // 计算经度差
    double dlon = lon2 - lon1;

    // 计算从飞机到目标的方位角
    double y = sin(dlon) * cos(lat2);
    double x = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(dlon);
    double target_bearing = atan2(y, x) * 180.0 / PI;

    // 将方位角归一化到 [0, 360) 范围
    if (target_bearing < 0) {
        target_bearing += 360.0;
    }

    // 计算角度差（目标方位角 - 飞机航向角）
    double angle_diff = target_bearing - aircraft_heading;

    // 将角度差归一化到 [-180, 180] 范围
    if (angle_diff > 180.0) {
        angle_diff -= 360.0;
    } else if (angle_diff < -180.0) {
        angle_diff += 360.0;
    }

    // 根据阈值判断方向
    if (angle_diff > threshold) {
        return 1;   // 右侧（顺时针）
    } else if (angle_diff < -threshold) {
        return -1;  // 左侧（逆时针）
    } else {
        return 0;   // 正前方或正后方
    }
}
// TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
int main() {
    // TIP Press <shortcut actionId="RenameElement"/> when your caret is at the
    // <b>lang</b> variable name to see how CLion can help you rename it.
    auto lang = "C++";
    std::cout << "Hello and welcome to " << lang << "!\n";

    for (int i = 1; i <= 5; i++) {
        // TIP Press <shortcut actionId="Release"/> to start debugging your code.
        // We have set one <icon src="AllIcons.Debugger.Db_set_breakpoint"/>
        // breakpoint for you, but you can always add more by pressing
        // <shortcut actionId="ToggleLineBreakpoint"/>.
        std::cout << "i = " << i << std::endl;
    }

    // 示例：飞机在北京，航向东北45度，目标在东侧
    double aircraft_lon = 116.4074;  // 北京经度
    double aircraft_lat = 39.9042;   // 北京纬度
    double aircraft_heading = 45.0;   // 东北方向

    double target_lon = 116.5074;    // 目标经度（东侧）
    double target_lat = 39.9042;     // 目标纬度（同纬度）

    int direction = getTargetDirection(aircraft_lon, aircraft_lat, aircraft_heading,
                                     target_lon, target_lat);

    // direction 结果：
    // -1: 目标在左侧
    //  0: 目标在正前方或正后方
    // +1: 目标在右侧
    std::cout << "direction = " << direction << std::endl;
    return 0;
}

// TIP See CLion help at <a
// href="https://www.jetbrains.com/help/clion/">jetbrains.com/help/clion/</a>.
//  Also, you can try interactive lessons for CLion by selecting
//  'Help | Learn IDE Features' from the main menu.